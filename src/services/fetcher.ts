export const fetcher = async (url: string) => {
  const response = await fetch(url);
  console.log(response, "this is a response from fetcher");
  if (!response.ok) {
    // Try to parse the error response from the backend
    let errorMessage = null;
    let errorData = null;
    if (response.status === 401) {
      if (typeof window !== "undefined") {
        window.location.href = "/auth/select-system";
      }
      throw new Error("Unauthorized");
    }

    if (response.status === 401) {
      if (typeof window !== "undefined") {
        window.location.href = "/auth/select-system";
      }
      throw new Error("Unauthorized");
    }

    if (response.status === 307 || response.status === 308) {
      const location = response.headers.get("Location");
      if (location) {
        window.location.href = location;
        return;
      }
    }
    try {
      // Attempt to get the error details from the response
      errorData = await response.json();
      errorMessage = errorData.error || errorData.message || errorMessage;
      errorMessage = errorMessage.data.error;
    } catch (parseError) {
      // If parsing fails, use the status text as fallback
      errorMessage = response.statusText || errorMessage;
    }

    const error = new Error(errorMessage);
    // Attach extra info to the error object
    (error as any).status = response.status;
    (error as any).data = errorData;

    throw error;
  }

  return response.json();
};
