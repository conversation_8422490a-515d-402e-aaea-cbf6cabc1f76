"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";
import { Permission } from "@/types/auth";
import { useUserPermission } from "@/app/[locale]/_modules/people/hooks/user/useUserPermission";
import {
  saveDataToSessionStorage,
  getDataFromSessionStorage,
} from "@/lib/local-storage";

interface PermissionContextType {
  hasPermission: (permission: string) => boolean;
  permissions: Permission[];
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
  clearPermissions: () => void;
}

const PermissionContext = createContext<PermissionContextType | null>(null);

const STORAGE_KEY = "user_permissions";

export const PermissionProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const {
    permission: apiPermissions,
    isLoading,
    error,
    mutate,
  } = useUserPermission();

  const [permissions, setPermissions] = useState<Permission[]>([]);

  // Load permissions from session storage on mount
  useEffect(() => {
    const storedPermissions =
      getDataFromSessionStorage<Permission[]>(STORAGE_KEY);
    if (storedPermissions) {
      setPermissions(storedPermissions);
    }
  }, []);

  // Clear permissions from both state and session storage
  const clearPermissions = useCallback(() => {
    setPermissions([]);
    if (typeof window !== "undefined") {
      sessionStorage.removeItem(STORAGE_KEY);
    }
  }, []);

  // Update permissions when API data changes
  useEffect(() => {
    if (apiPermissions && apiPermissions.length > 0) {
      saveDataToSessionStorage(STORAGE_KEY, apiPermissions);
      setPermissions(apiPermissions);
    }
  }, [apiPermissions]);

  const hasPermission = useCallback(
    (perm: string) => {
      return permissions.some((p) => p.id === perm);
    },
    [permissions],
  );

  return (
    <PermissionContext.Provider
      value={{
        permissions,
        hasPermission,
        isLoading,
        error,
        refetch: mutate,
        clearPermissions,
      }}
    >
      {children}
    </PermissionContext.Provider>
  );
};

export const usePermission = (): PermissionContextType => {
  const ctx = useContext(PermissionContext);
  if (!ctx)
    throw new Error("usePermission must be used within PermissionProvider");
  return ctx;
};
